// utils/auth.ts

export interface LoginResponse {
  message: string;
  customToken: string;
  instructions: string;
  user: {
    userId: string;
    email: string;
    username: string;
    role: string;
  };
}

export interface ConnectionStatus {
  ip: string;
  status: string;
}

/**
 * Handle login response and save authentication data to localStorage
 * @param loginData - The login response data
 */
export const handleLoginResponse = (loginData: LoginResponse): void => {
  if (loginData.user && loginData.customToken) {
    // Save essential auth data
    localStorage.setItem('userId', loginData.user.userId);
    localStorage.setItem('customToken', loginData.customToken);
    
    // Save additional user data
    localStorage.setItem('userEmail', loginData.user.email);
    localStorage.setItem('username', loginData.user.username);
    localStorage.setItem('userRole', loginData.user.role);
    
    console.log('Login data saved to localStorage');
  }
};

/**
 * Save robot IP addresses to localStorage
 * @param ips - Array of IP connection statuses
 */
export const saveRobotIPs = (ips: ConnectionStatus[]): void => {
  if (ips && ips.length > 0) {
    localStorage.setItem('robotIPs', JSON.stringify(ips));
    console.log('Robot IPs saved to localStorage:', ips);
  }
};

/**
 * Get saved robot IPs from localStorage
 * @returns Array of saved IP connection statuses
 */
export const getSavedRobotIPs = (): ConnectionStatus[] => {
  try {
    const saved = localStorage.getItem('robotIPs');
    return saved ? JSON.parse(saved) : [];
  } catch (error) {
    console.error('Error parsing saved robot IPs:', error);
    return [];
  }
};

/**
 * Get saved user authentication data from localStorage
 * @returns Object containing user auth data
 */
export const getSavedAuthData = () => {
  return {
    userId: localStorage.getItem('userId'),
    customToken: localStorage.getItem('customToken'),
    userEmail: localStorage.getItem('userEmail'),
    username: localStorage.getItem('username'),
    userRole: localStorage.getItem('userRole'),
  };
};

/**
 * Clear all saved authentication and robot data from localStorage
 */
export const clearSavedData = (): void => {
  localStorage.removeItem('userId');
  localStorage.removeItem('customToken');
  localStorage.removeItem('userEmail');
  localStorage.removeItem('username');
  localStorage.removeItem('userRole');
  localStorage.removeItem('robotIPs');
  console.log('All saved data cleared from localStorage');
};

/**
 * Check if user is authenticated (has valid tokens)
 * @returns boolean indicating if user is authenticated
 */
export const isAuthenticated = (): boolean => {
  const userId = localStorage.getItem('userId');
  const customToken = localStorage.getItem('customToken');
  return !!(userId && customToken);
};
