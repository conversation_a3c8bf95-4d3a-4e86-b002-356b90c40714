// components/RobotInterface.tsx
"use client"

import { useState, useEffect } from 'react';
import { <PERSON>, <PERSON>, Scan, Wifi } from 'lucide-react';
import { saveRobotIPs } from '../utils/auth';

interface RobotInterfaceProps {
  onStartScan: () => void;
}

interface ConnectionStatus {
  ip: string;
  status: string;
}

interface ApiResponse {
  ips: ConnectionStatus[];
}

export function RobotInterface({ onStartScan }: RobotInterfaceProps) {
  const [isHovered, setIsHovered] = useState(false);
  const [isConnected, setIsConnected] = useState(false);
  const [connectionData, setConnectionData] = useState<ConnectionStatus[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  // Function to check ROS2 connection status
  const checkConnectionStatus = async () => {
    try {
      setIsLoading(true);
      const response = await fetch('http://localhost:3001/api/v1/api/connect-ros2');
      const data: ApiResponse = await response.json();

      // Check if we have any connected IPs
      const hasConnectedIPs = data.ips && data.ips.length > 0 &&
        data.ips.some(ip => ip.status === 'connected');

      setIsConnected(hasConnectedIPs);
      setConnectionData(data.ips || []);

      // Save IP addresses to localStorage using utility function
      saveRobotIPs(data.ips || []);
    } catch (error) {
      console.error('Failed to check connection status:', error);
      setIsConnected(false);
      setConnectionData([]);
    } finally {
      setIsLoading(false);
    }
  };

  // Check connection status on component mount and set up polling
  useEffect(() => {
    checkConnectionStatus();

    // Poll every 5 seconds to check connection status
    const interval = setInterval(checkConnectionStatus, 5000);

    return () => clearInterval(interval);
  }, []);

  return (
    <div className="min-h-screen robot-interface-container flex flex-col">
      {/* Header Section */}
      <header className="p-6 flex items-center justify-between">
        {/* Left side - SteriBOT Logo */}
        <div className="flex items-center gap-4">
          <div className="w-12 h-12 rounded-full overflow-hidden bg-white/10 flex items-center justify-center">
            <img 
              src="/images/image.png" 
              alt="SteriBOT Logo" 
              className="w-10 h-10 object-cover rounded-full"
              onError={(e) => {
                e.currentTarget.style.display = 'none';
                (e.currentTarget.nextElementSibling as HTMLElement)!.style.display = 'flex';
              }}
            />
            <div 
              className="w-10 h-10 bg-white/20 rounded-full items-center justify-center text-white font-bold text-lg hidden"
            >
              S
            </div>
          </div>
          <div>
            <h1 className="text-2xl font-bold text-white">SteriBOT</h1>
          </div>
        </div>

        {/* Right side - Connection Status */}
        <div className="flex items-center gap-3 bg-white/10 backdrop-blur-sm rounded-full px-4 py-2 border border-white/20">
          <div className={`w-3 h-3 rounded-full animate-pulse ${
            isConnected ? 'bg-green-400' : 'bg-red-400'
          }`}></div>
          <span className="text-white font-medium">
            {isLoading ? 'Checking...' : (isConnected ? 'Connected' : 'Disconnected')}
          </span>
          <Wifi className="w-4 h-4 text-white" />
        </div>
      </header>

      {/* Main Content Section */}
      <div className="flex-1 flex flex-col items-center justify-center px-8">
        <div className="w-full max-w-2xl text-center">
          {/* Robot Image */}
          <div className="mb-8 flex justify-center">
            <div className="relative">
              <div className="w-64 h-64 rounded-2xl bg-white/10 backdrop-blur-sm flex items-center justify-center robot-glow border-2 border-white/20">
                <img 
                  src="/images/image.png" 
                  alt="SteriBOT Robot" 
                  className="w-48 h-48 object-cover rounded-xl"
                  onError={(e) => {
                    e.currentTarget.style.display = 'none';
                    (e.currentTarget.nextElementSibling as HTMLElement)!.style.display = 'flex';
                  }}
                />
                <div 
                  className="w-48 h-48 bg-gradient-to-br from-white/20 to-white/5 rounded-xl items-center justify-center text-white font-bold text-6xl hidden"
                >
                  
                </div>
              </div>
              {/* Floating animation rings */}
              <div className="absolute inset-0 rounded-2xl border-2 border-teal-400/30 animate-ping"></div>
              <div className="absolute inset-2 rounded-2xl border border-teal-300/20 animate-pulse"></div>
            </div>
          </div>

          {/* Robot Name and Description */}
          <div className="mb-8">
            <h2 className="text-4xl font-bold gradient-text mb-3">
              S4-CDZ12RR
            </h2>
            <p className="text-teal-200 text-xl font-medium">
              Autonomous disinfection robot
            </p>
          </div>

          {/* Status Indicators Row */}
          <div className="flex items-center justify-center gap-8 mb-12">
            {/* Battery Status */}
            <div className="flex items-center gap-3 bg-white/10 backdrop-blur-sm rounded-full px-6 py-3 border border-white/20">
              <Battery className={`w-6 h-6 ${isConnected ? 'text-green-400' : 'text-red-400'}`} />
              <span className="text-white font-semibold text-lg">
                {isConnected ? '82%' : '0%'}
              </span>
            </div>

            {/* Speed Status */}
            <div className="flex items-center gap-3 bg-white/10 backdrop-blur-sm rounded-full px-6 py-3 border border-white/20">
              <Clock className="w-6 h-6 text-blue-400" />
              <span className="text-white font-semibold text-lg">0.8m/s</span>
            </div>

            {/* Active Status */}
            <div className="flex items-center gap-3 bg-white/10 backdrop-blur-sm rounded-full px-6 py-3 border border-white/20">
              <div className={`w-3 h-3 rounded-full animate-pulse ${
                isConnected ? 'bg-green-400' : 'bg-red-400'
              }`}></div>
              <span className="text-white font-semibold text-lg">
                {isConnected ? 'Active' : 'Inactive'}
              </span>
            </div>
          </div>

          {/* Start Scan Button */}
          <button
            onClick={isConnected ? onStartScan : undefined}
            onMouseEnter={() => isConnected && setIsHovered(true)}
            onMouseLeave={() => setIsHovered(false)}
            disabled={!isConnected}
            className={`group relative overflow-hidden backdrop-blur-sm border-2 rounded-2xl px-12 py-6 font-bold text-xl transition-all duration-300 ${
              isConnected
                ? 'bg-white/10 border-white/20 text-white hover:scale-105 hover:bg-white/20 start-scan-button cursor-pointer'
                : 'bg-gray-500/20 border-gray-500/40 text-gray-400 cursor-not-allowed'
            }`}
          >
            {/* Animated border effect - only when connected */}
            {isConnected && (
              <>
                <div className="absolute inset-0 rounded-2xl bg-gradient-to-r from-teal-400 via-blue-500 to-teal-400 opacity-0 group-hover:opacity-100 transition-opacity duration-300 animate-gradient-x"></div>
                <div className="absolute inset-[2px] rounded-2xl bg-gradient-to-br from-teal-600/80 to-blue-600/80 group-hover:from-teal-500/90 group-hover:to-blue-500/90 transition-all duration-300"></div>
              </>
            )}

            {/* Button content */}
            <div className="relative flex items-center gap-4">
              <Scan className={`w-8 h-8 transition-transform duration-300 ${
                isConnected && isHovered ? 'rotate-180' : ''
              } ${isConnected ? 'text-white' : 'text-gray-500'}`} />
              <span>Start Scan</span>
            </div>

            {/* Pulse effect - only when connected */}
            {isConnected && (
              <div className="absolute inset-0 rounded-2xl bg-white/10 opacity-0 group-hover:opacity-100 group-hover:animate-ping"></div>
            )}
          </button>
        </div>
      </div>

      {/* Floating Particles Animation */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        {[...Array(15)].map((_, i) => (
          <div
            key={i}
            className="absolute w-1 h-1 bg-white/20 rounded-full"
            style={{
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`,
              animation: `float ${4 + Math.random() * 3}s ease-in-out infinite`,
              animationDelay: `${Math.random() * 2}s`
            }}
          />
        ))}
      </div>

      <style jsx>{`
        @keyframes float {
          0%, 100% {
            transform: translateY(0px) rotate(0deg);
            opacity: 0.1;
          }
          50% {
            transform: translateY(-30px) rotate(180deg);
            opacity: 0.3;
          }
        }

        @keyframes gradient-x {
          0%, 100% {
            background-position: 0% 50%;
          }
          50% {
            background-position: 100% 50%;
          }
        }

        .robot-interface-container {
          background: linear-gradient(135deg, #0A3F4C 0%, #0C6980 50%, #14b8a6 100%);
          background-size: 400% 400%;
          animation: backgroundShift 8s ease infinite;
        }

        .robot-glow {
          box-shadow: 0 0 30px rgba(20, 184, 166, 0.3);
        }

        .start-scan-button {
          box-shadow: 0 10px 30px rgba(20, 184, 166, 0.2);
        }

        .start-scan-button:hover {
          box-shadow: 0 15px 40px rgba(20, 184, 166, 0.4);
        }

        .animate-gradient-x {
          background-size: 200% 200%;
          animation: gradient-x 2s ease infinite;
        }

        @keyframes backgroundShift {
          0%, 100% {
            background-position: 0% 50%;
          }
          50% {
            background-position: 100% 50%;
          }
        }
      `}</style>
    </div>
  );
}
