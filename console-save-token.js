// console-save-token.js
// Copy and paste this directly into your browser console to save the token and userId

// Your login data
const loginData = {
  customToken: "eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9.***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.EsnKXxbb3W1VPrDshP4LQQxwI-2gTqFmTkNNZ8uQIxpdcXibgd5ehi-UwrmIEfL7OpD4XZMsXcdFYyV0fX3ExRvW7KitdIwuqcBAODoO4kuJba53DCf5pv-CC4xdNc89rBcQGyn3ZgEQZBBEUFbsCWtVkCRjyGqOs3H5foeIjbTxQsDhf05pxqoP6CWwt6K_gTQglEktF6zCwwatMtw3zKcdJuUIdBxrIB4J9g_ZzmvEH9dr6PrIVIf_fCTVxmichkDq_G5FInJuJrg1nYpPuPzVZiytGjpcG-Uu0qE2vAMDxBmaqs20gjqwE4O__dqmKRAhIZx2rNojzx1FDpNyOQ",
  userId: "btNUn3XDVgRtuEWa4K6MzPax7lk2"
};

// Save to localStorage
localStorage.setItem('customToken', loginData.customToken);
localStorage.setItem('userId', loginData.userId);

// Log success
console.log('✅ Login data saved to localStorage successfully!');
console.log('📝 CustomToken saved (length):', loginData.customToken.length, 'characters');
console.log('👤 UserId saved:', loginData.userId);

// Verify the data was saved correctly
const savedToken = localStorage.getItem('customToken');
const savedUserId = localStorage.getItem('userId');

console.log('\n🔍 Verification:');
console.log('Token saved correctly:', savedToken === loginData.customToken ? '✅ YES' : '❌ NO');
console.log('UserId saved correctly:', savedUserId === loginData.userId ? '✅ YES' : '❌ NO');

// Show what's currently in localStorage
console.log('\n📦 Current localStorage contents:');
console.log('customToken:', localStorage.getItem('customToken') ? 'SAVED ✅' : 'NOT FOUND ❌');
console.log('userId:', localStorage.getItem('userId') ? 'SAVED ✅' : 'NOT FOUND ❌');

// Optional: Show all localStorage keys
console.log('\n🗂️ All localStorage keys:', Object.keys(localStorage));
