// examples/login-usage-example.tsx
// This is an example of how to use the handleLoginResponse utility function

import { handleLoginResponse, LoginResponse } from '../utils/auth';

// Example usage in your login component
export const LoginComponent = () => {
  const handleLogin = async (credentials: { email: string; password: string }) => {
    try {
      const response = await fetch('your-login-api-endpoint', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(credentials),
      });

      const loginData: LoginResponse = await response.json();

      if (response.ok && loginData.message === 'Login successful') {
        // Use the utility function to save login data to localStorage
        handleLoginResponse(loginData);
        
        // Redirect or update UI state
        console.log('Login successful, data saved to localStorage');
        // router.push('/dashboard') or similar
      } else {
        console.error('Login failed:', loginData.message);
      }
    } catch (error) {
      console.error('Login error:', error);
    }
  };

  // Your login form JSX here...
  return (
    <div>
      {/* Your login form */}
    </div>
  );
};

// Example of the expected login response format:
/*
{
  "message": "Login successful",
  "customToken": "eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9...",
  "instructions": "Use this custom token to authenticate with Firebase Auth SDK...",
  "user": {
    "userId": "btNUn3XDVgRtuEWa4K6MzPax7lk2",
    "email": "<EMAIL>",
    "username": "nawel",
    "role": "user"
  }
}
*/
