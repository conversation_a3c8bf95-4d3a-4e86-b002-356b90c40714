"use client"

import { useState } from "react"
import { SidebarProvider, SidebarInset } from "@/components/ui/sidebar"
import { AppSidebar } from "@/components/app-sidebar"
import { Dashboard } from "@/components/dashboard"
import { Profile } from "@/components/profile"
import { RobotDetails } from "@/components/robot-details"
import { RobotsList } from "@/components/robots-list"
import { SterilizationHistory } from "@/components/sterilization-history"
import { ObstacleDetection } from "@/components/obstacle-detection"
import { Home } from "@/components/home"
import { Login } from "@/components/Login"

export default function Page() {
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [activeView, setActiveView] = useState("/home")

  const handleLogin = (email: string, password: string) => {
    // Here you would typically validate credentials with your backend
    // For demo purposes, we'll accept any valid email/password combination
    console.log('Login attempt:', { email, password });
    setIsAuthenticated(true);
  };

  const handleLogout = () => {
    setIsAuthenticated(false);
    setActiveView("/dashboard");
  };

  // Show login screen if not authenticated
  if (!isAuthenticated) {
    return <Login onLogin={handleLogin} />;
  }

  const renderContent = () => {
    switch (activeView) {
       case "/home":
        return <Home />
      case "/dashboard":
        return <Dashboard />
      case "/profile":
        return <Profile />
      case "/robot-details":
        return <RobotDetails />
      case "/robots-list":
        return <RobotsList />
      case "/sterilization-history":
        return <SterilizationHistory />
      case "/obstacle-detection":
        return <ObstacleDetection />
     
      default:
        return <Dashboard />
    }
  }

  return (
    <SidebarProvider>
      <AppSidebar
        activeItem={activeView}
        onItemClick={setActiveView}
        onLogout={handleLogout}
      />
      <SidebarInset>{renderContent()}</SidebarInset>
    </SidebarProvider>
  )
}