// components/ui/NetworkConfigModal.tsx
import { useState } from 'react';
import { Wifi, Smartphone, Check } from 'lucide-react';
import { Button } from "@/components/ui/button";

interface NetworkConfigModalProps {
  isOpen: boolean;
  onClose: () => void;
  onConnect: (connectionType: 'wifi' | 'mobile') => void;
}

export function NetworkConfigModal({ isOpen, onClose, onConnect }: NetworkConfigModalProps) {
  const [selectedConnection, setSelectedConnection] = useState<'wifi' | 'mobile' | null>(null);
  const [isConnecting, setIsConnecting] = useState(false);

  const handleConnect = async () => {
    if (!selectedConnection) return;

    setIsConnecting(true);
    
    // Simulate connection process
    setTimeout(() => {
      onConnect(selectedConnection);
      setIsConnecting(false);
    }, 2000);
  };

  const handleClose = () => {
    if (!isConnecting) {
      setSelectedConnection(null);
      onClose();
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center p-4 z-50">
      <div className="relative z-10 w-full max-w-md">
        <div className="network-modal-3d rounded-xl p-8 border border-teal-200/30 bg-teal-50/95 backdrop-blur-md shadow-3d modal-fade-in">
          {/* 3D Depth Layers */}
          <div className="absolute inset-0 rounded-xl bg-gradient-to-br from-white/20 to-transparent pointer-events-none"></div>
          <div className="absolute inset-1 rounded-xl bg-gradient-to-tl from-teal-100/30 to-transparent pointer-events-none"></div>
          <div className="relative z-10">
            {/* Logo/Title Section */}
            <div className="text-center mb-8">
              <div className="flex items-center justify-center gap-3 mb-4">
                <div className="w-12 h-12 rounded-full overflow-hidden bg-gradient-to-br from-teal-600 to-teal-800 flex items-center justify-center shadow-lg">
                  <img
                    src="/images/image.png"
                    alt="SteriBOT Logo"
                    className="w-10 h-10 object-cover rounded-full"
                    onError={(e) => {
                      e.currentTarget.style.display = 'none';
                      e.currentTarget.nextElementSibling!.style.display = 'flex';
                    }}
                  />
                  <div
                    className="w-10 h-10 bg-gradient-to-br from-teal-600 to-teal-800 rounded-full items-center justify-center text-white font-bold text-lg hidden"
                  >
                    S
                  </div>
                </div>
                <div>
                  <h1 className="text-2xl font-bold text-teal-800">SteriBOT</h1>
                  <p className="text-teal-600 text-sm">STERILISER</p>
                </div>
              </div>
              <h2 className="text-xl font-semibold text-teal-800 mb-2">Connect with</h2>
              <p className="text-teal-700 text-sm">
                Please connect to a WiFi network or enable your mobile data (4G/5G)
              </p>
            </div>

            {/* Connection Options */}
            <div className="space-y-4 mb-8">
              {/* WiFi Option */}
              <div
                onClick={() => !isConnecting && setSelectedConnection('wifi')}
                className={`relative p-4 rounded-lg border-2 cursor-pointer transition-all duration-300 shadow-lg ${
                  selectedConnection === 'wifi'
                    ? 'border-teal-600 bg-gradient-to-r from-teal-600 to-teal-700 text-white'
                    : 'border-teal-300 bg-white/80 hover:border-teal-500 hover:bg-white/90'
                } ${isConnecting ? 'cursor-not-allowed opacity-50' : ''}`}
              >
                <div className="flex items-center gap-4">
                  <div className={`w-12 h-12 rounded-full flex items-center justify-center transition-colors ${
                    selectedConnection === 'wifi' ? 'bg-white/20' : 'bg-teal-100'
                  }`}>
                    <Wifi className={`w-6 h-6 ${selectedConnection === 'wifi' ? 'text-white' : 'text-teal-600'}`} />
                  </div>
                  <div className="flex-1">
                    <h3 className={`font-medium ${selectedConnection === 'wifi' ? 'text-white' : 'text-teal-800'}`}>Connect with WiFi</h3>
                    <p className={`text-sm ${selectedConnection === 'wifi' ? 'text-teal-100' : 'text-teal-600'}`}>Use wireless network connection</p>
                  </div>
                  {selectedConnection === 'wifi' && (
                    <div className="w-6 h-6 rounded-full bg-white flex items-center justify-center">
                      <Check className="w-4 h-4 text-teal-600" />
                    </div>
                  )}
                </div>
              </div>

              {/* Mobile Data Option */}
              <div
                onClick={() => !isConnecting && setSelectedConnection('mobile')}
                className={`relative p-4 rounded-lg border-2 cursor-pointer transition-all duration-300 shadow-lg ${
                  selectedConnection === 'mobile'
                    ? 'border-teal-600 bg-gradient-to-r from-teal-600 to-teal-700 text-white'
                    : 'border-teal-300 bg-white/80 hover:border-teal-500 hover:bg-white/90'
                } ${isConnecting ? 'cursor-not-allowed opacity-50' : ''}`}
              >
                <div className="flex items-center gap-4">
                  <div className={`w-12 h-12 rounded-full flex items-center justify-center transition-colors ${
                    selectedConnection === 'mobile' ? 'bg-white/20' : 'bg-teal-100'
                  }`}>
                    <Smartphone className={`w-6 h-6 ${selectedConnection === 'mobile' ? 'text-white' : 'text-teal-600'}`} />
                  </div>
                  <div className="flex-1">
                    <h3 className={`font-medium ${selectedConnection === 'mobile' ? 'text-white' : 'text-teal-800'}`}>Connect with Mobile Data</h3>
                    <p className={`text-sm ${selectedConnection === 'mobile' ? 'text-teal-100' : 'text-teal-600'}`}>Use cellular network (4G/5G)</p>
                  </div>
                  {selectedConnection === 'mobile' && (
                    <div className="w-6 h-6 rounded-full bg-white flex items-center justify-center">
                      <Check className="w-4 h-4 text-teal-600" />
                    </div>
                  )}
                </div>
              </div>
            </div>

            {/* Action Button */}
            <div className="w-full">
              <Button
                onClick={handleConnect}
                disabled={!selectedConnection || isConnecting}
                className="w-full text-white py-3 px-4 disabled:opacity-50 disabled:cursor-not-allowed connect-button transition-all hover:scale-105 shadow-lg"
                style={{ background: 'linear-gradient(90deg, #0A3F4C, #0C6980)' }}
              >
                {isConnecting ? (
                  <div className="flex items-center justify-center gap-2">
                    <div className="w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin"></div>
                    Connecting...
                  </div>
                ) : (
                  'Connect'
                )}
              </Button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
