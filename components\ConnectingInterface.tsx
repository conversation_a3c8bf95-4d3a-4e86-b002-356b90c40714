// components/ConnectingInterface.tsx
"use client"

import { useEffect, useState } from 'react';
import { Wifi, ArrowR<PERSON>, Zap } from 'lucide-react';

interface ConnectingInterfaceProps {
  connectionType: 'wifi' | 'mobile';
  onConnectionComplete: () => void;
}

export function ConnectingInterface({ connectionType, onConnectionComplete }: ConnectingInterfaceProps) {
  const [progress, setProgress] = useState(0);
  const [currentStep, setCurrentStep] = useState(0);
  const [connectionError, setConnectionError] = useState<string | null>(null);

  const steps = [
    "Initializing connection...",
    "Scanning for devices...",
    "Establishing secure link...",
    "Synchronizing data...",
    "Connection established!"
  ];

  const connectToROS2 = async () => {
    try {
      const response = await fetch('http://localhost:3001/api/v1/api/connect-ros2', {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('authToken')}`
        },
      
      });

      if (!response.ok) {
        throw new Error(`Connection failed: ${response.statusText}`);
      }

      const data = await response.json();
      console.log(' connection successful:', data);
      return data;
    } catch (error) {
      console.error(' connection error:', error);
      setConnectionError(error instanceof Error ? error.message : 'Connection failed');
      throw error;
    }
  };

  useEffect(() => {
    let timer: NodeJS.Timeout;
    let stepTimer: NodeJS.Timeout;

    const startConnection = async () => {
      try {
        // Start progress animation
        timer = setInterval(() => {
          setProgress(prev => {
            if (prev >= 90) {
              clearInterval(timer);
              return 90; // Stop at 90% until API call completes
            }
            return prev + 2;
          });
        }, 100);

        // Start step animation
        stepTimer = setInterval(() => {
          setCurrentStep(prev => {
            if (prev >= steps.length - 2) {
              clearInterval(stepTimer);
              return prev;
            }
            return prev + 1;
          });
        }, 1000);

        // Wait for progress to reach 90% then call API
        setTimeout(async () => {
          try {
            await connectToROS2();
            
            // Complete progress and final step
            setProgress(100);
            setCurrentStep(steps.length - 1);
            
            // Wait a moment then complete
            setTimeout(() => onConnectionComplete(), 1000);
          } catch (error) {
            // Handle connection error
            clearInterval(timer);
            clearInterval(stepTimer);
          }
        }, 4500); // Wait ~4.5 seconds before API call

      } catch (error) {
        setConnectionError('Failed to start connection process');
      }
    };

    startConnection();

    return () => {
      clearInterval(timer);
      clearInterval(stepTimer);
    };
  }, [connectionType, onConnectionComplete]);

  // Show error state if connection failed
  if (connectionError) {
    return (
      <div className="min-h-screen connecting-container-inverted flex flex-col items-center justify-center">
        <div className="text-center p-8">
          <div className="w-16 h-16 bg-red-500 rounded-full flex items-center justify-center mx-auto mb-4">
            <span className="text-white text-2xl">✕</span>
          </div>
          <h2 className="text-xl font-bold text-red-600 mb-2">Connection Failed</h2>
          <p className="text-red-500 mb-4">{connectionError}</p>
          <button 
            onClick={() => window.location.reload()} 
            className="px-6 py-2 bg-teal-600 text-white rounded-lg hover:bg-teal-700"
          >
            Retry Connection
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen connecting-container-inverted flex flex-col">
      {/* Blurred Background */}
      <div className="absolute inset-0 connecting-background"></div>
      
      {/* Header Section */}
      <header className="relative z-10 p-6 flex items-center gap-4">
        <div className="w-12 h-12 rounded-full overflow-hidden bg-gradient-to-br from-teal-600 to-teal-800 flex items-center justify-center shadow-lg">
          <img 
            src="/images/image.png" 
            alt="SteriBOT Logo" 
            className="w-10 h-10 object-cover rounded-full"
            onError={(e) => {
              e.currentTarget.style.display = 'none';
              (e.currentTarget.nextElementSibling as HTMLElement)!.style.display = 'flex';
            }}
          />
          <div 
            className="w-10 h-10 bg-gradient-to-br from-teal-600 to-teal-800 rounded-full items-center justify-center text-white font-bold text-lg hidden"
          >
            S
          </div>
        </div>
        <div>
          <h1 className="text-2xl font-bold text-teal-800">SteriBOT</h1>
          <p className="text-teal-600 text-sm">Connecting robot</p>
        </div>
      </header>

      {/* Center Animation Section with 3D Card */}
      <div className="relative z-10 flex-1 flex items-center justify-center px-8">
        <div className="connecting-card-3d w-full max-w-4xl bg-white/10 backdrop-blur-md rounded-2xl p-8 border border-teal-200/30 shadow-3d">
          {/* 3D Depth Layers */}
          <div className="absolute inset-0 rounded-2xl bg-gradient-to-br from-white/20 to-transparent pointer-events-none"></div>
          <div className="absolute inset-1 rounded-2xl bg-gradient-to-tl from-teal-100/20 to-transparent pointer-events-none"></div>
          
          <div className="relative z-10">
            {/* Connection Flow Animation */}
            <div className="flex items-center justify-center gap-16 mb-16">
              {/* WiFi/Mobile Icon */}
              <div className="relative">
                <div className="w-24 h-24 rounded-full bg-gradient-to-br from-teal-600/80 to-teal-800/80 backdrop-blur-sm flex items-center justify-center wifi-pulse border-2 border-teal-400/30 shadow-lg">
                  {connectionType === 'wifi' ? (
                    <Wifi className="w-12 h-12 text-white" />
                  ) : (
                    <Zap className="w-12 h-12 text-white" />
                  )}
                </div>
                <div className="absolute -bottom-6 left-1/2 transform -translate-x-1/2">
                  <span className="text-teal-800 text-sm font-medium">
                    {connectionType === 'wifi' ? 'WiFi' : 'Mobile Data'}
                  </span>
                </div>
              </div>

              {/* Animated Arrows */}
              <div className="flex items-center gap-4 flow-arrows">
                <ArrowRight className="w-8 h-8 text-teal-600" />
                <ArrowRight className="w-8 h-8 text-teal-700" />
                <ArrowRight className="w-8 h-8 text-teal-800" />
              </div>

              {/* Robot/Sterilizer */}
              <div className="relative">
                <div className="w-32 h-32 rounded-full bg-gradient-to-br from-teal-600/80 to-teal-800/80 backdrop-blur-sm flex items-center justify-center robot-waves border-2 border-teal-400/30 shadow-lg">
                  <div className="w-20 h-20 rounded-lg bg-gradient-to-br from-white/20 to-white/5 flex items-center justify-center">
                    <img 
                      src="/images/image.png" 
                      alt="SteriBOT Device" 
                      className="w-16 h-16 object-cover rounded-lg"
                      onError={(e) => {
                        e.currentTarget.style.display = 'none';
                        (e.currentTarget.nextElementSibling as HTMLElement)!.style.display = 'flex';
                      }}
                    />
                    <div 
                      className="w-16 h-16 bg-gradient-to-br from-teal-400 to-teal-600 rounded-lg items-center justify-center text-white font-bold text-2xl hidden"
                    >
                      S
                    </div>
                  </div>
                </div>
                <div className="absolute -bottom-6 left-1/2 transform -translate-x-1/2">
                  <span className="text-teal-800 text-sm font-medium">SteriBOT</span>
                </div>
              </div>
            </div>

            {/* Progress Section */}
            <div className="text-center mb-8">
              <div className="w-full max-w-md mx-auto mb-6">
                <div className="w-full bg-teal-200/30 rounded-full h-3 backdrop-blur-sm border border-teal-300/40">
                  <div 
                    className="h-full rounded-full transition-all duration-300 ease-out"
                    style={{ 
                      width: `${progress}%`,
                      background: 'linear-gradient(90deg, #14b8a6, #0C6980)'
                    }}
                  ></div>
                </div>
                <div className="mt-2 text-teal-800 text-sm font-medium">
                  {progress}% Complete
                </div>
              </div>

              {/* Current Step */}
              <div className="mb-4">
                <h2 className="text-xl font-semibold text-teal-800 mb-2">
                  {steps[currentStep]}
                </h2>
                <div className="flex justify-center gap-2">
                  {steps.map((_, index) => (
                    <div
                      key={index}
                      className={`w-2 h-2 rounded-full transition-all duration-300 ${
                        index <= currentStep 
                          ? 'bg-teal-600 scale-110' 
                          : 'bg-teal-300/50'
                      }`}
                    />
                  ))}
                </div>
              </div>
            </div>

            {/* Bottom Text Section */}
            <div className="text-center">
              <h3 className="text-2xl font-bold gradient-text mb-2">
                Looking for your smart device
              </h3>
              <p className="text-teal-700 text-lg">
                Please ensure your SteriBOT device is powered on and within range
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Floating Particles Animation */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        {[...Array(20)].map((_, i) => (
          <div
            key={i}
            className="absolute w-1 h-1 bg-teal-400/30 rounded-full"
            style={{
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`,
              animation: `float ${3 + Math.random() * 4}s ease-in-out infinite`,
              animationDelay: `${Math.random() * 2}s`
            }}
          />
        ))}
      </div>

      <style jsx>{`
        @keyframes float {
          0%, 100% {
            transform: translateY(0px) rotate(0deg);
            opacity: 0.1;
          }
          50% {
            transform: translateY(-20px) rotate(180deg);
            opacity: 0.3;
          }
        }
      `}</style>
    </div>
  );
}


